<?php

/**
 * The admin-specific functionality of the plugin.
 *
 * @package    Learnpressium
 * @subpackage Learnpressium/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the admin-specific stylesheet and JavaScript.
 */
class Learnpressium_Admin {

    /**
     * The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Register the stylesheets for the admin area.
     */
    public function enqueue_styles() {
        wp_enqueue_style(
            $this->plugin_name,
            LEARNPRESSIUM_PLUGIN_URL . 'admin/css/learnpressium-admin.css',
            array(),
            $this->version,
            'all'
        );
    }

    /**
     * Register the JavaScript for the admin area.
     */
    public function enqueue_scripts() {
        wp_enqueue_script(
            $this->plugin_name,
            LEARNPRESSIUM_PLUGIN_URL . 'admin/js/learnpressium-admin.js',
            array('jquery'),
            $this->version,
            false
        );
    }
}
